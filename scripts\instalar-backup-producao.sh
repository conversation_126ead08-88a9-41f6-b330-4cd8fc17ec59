#!/bin/bash
# Script para instalar e configurar o sistema de backup no servidor de produção
# Este script deve ser executado localmente

# Configurações
SERVIDOR="semfronteiras@***********"
PORTA="40666"
DIRETORIO_REMOTO="/var/www/sem-fronteiras-ssh"

echo "Iniciando instalação do sistema de backup no servidor de produção..."

# 1. Fazer upload dos scripts para o servidor
echo "Enviando scripts para o servidor..."
scp -P $PORTA backup-externo.sh $SERVIDOR:$DIRETORIO_REMOTO/
scp -P $PORTA configurar-backup-diario.sh $SERVIDOR:$DIRETORIO_REMOTO/
scp -P $PORTA README_BACKUP.md $SERVIDOR:$DIRETORIO_REMOTO/

# 2. Tornar os scripts executáveis no servidor
echo "Tornando os scripts executáveis..."
ssh -p $PORTA $SERVIDOR "chmod +x $DIRETORIO_REMOTO/backup-externo.sh"
ssh -p $PORTA $SERVIDOR "chmod +x $DIRETORIO_REMOTO/configurar-backup-diario.sh"

# 3. Criar diretório de backup externo
echo "Criando diretório de backup externo..."
ssh -p $PORTA $SERVIDOR "sudo mkdir -p /var/backups/sem-fronteiras"
ssh -p $PORTA $SERVIDOR "sudo chmod 755 /var/backups/sem-fronteiras"

# 4. Executar o script de configuração
echo "Configurando backup diário..."
ssh -p $PORTA $SERVIDOR "cd $DIRETORIO_REMOTO && sudo bash configurar-backup-diario.sh"

echo "Instalação concluída!"
echo "Verifique o arquivo README_BACKUP.md para mais informações sobre o sistema de backup."

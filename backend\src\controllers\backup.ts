import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient, Role } from '@prisma/client'
import { ServerBackupData, ImportServerDTO, BackupValidationResult } from '../types/backup'

const prisma = new PrismaClient()

/**
 * Exporta um servidor específico como backup JSON
 */
export async function exportServer(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    // Buscar informações do usuário atual
    const currentUser = await prisma.user.findUnique({
      where: { id: request.user.id },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    if (!currentUser) {
      return reply.status(404).send({ error: 'Usuário não encontrado' })
    }

    // Buscar o servidor com seus comandos
    const server = await prisma.server.findUnique({
      where: { id },
      include: {
        commands: {
          where: { isLatest: true },
          orderBy: { order: 'asc' }
        }
      },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário tem acesso ao servidor
    if (request.user.role !== Role.ADMIN && server.userId !== request.user.id) {
      return reply.status(403).send({ error: 'Acesso negado a este servidor' })
    }

    // Criar estrutura de backup
    const backupData: ServerBackupData = {
      version: '1.0.0',
      exportedAt: new Date().toISOString(),
      exportedBy: {
        id: currentUser.id,
        name: currentUser.name,
        email: currentUser.email
      },
      server: {
        name: server.name,
        host: server.host,
        port: server.port,
        username: server.username,
        password: server.password || undefined,
        privateKey: server.privateKey || undefined,
        os: server.os,
        deviceType: server.deviceType
      },
      commands: server.commands.map(cmd => ({
        name: cmd.name,
        command: cmd.command,
        description: cmd.description || undefined,
        order: cmd.order
      }))
    }

    // Definir nome do arquivo
    const fileName = `backup_${server.name.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.json`

    return reply
      .header('Content-Type', 'application/json')
      .header('Content-Disposition', `attachment; filename="${fileName}"`)
      .send(backupData)

  } catch (error) {
    console.error('Erro ao exportar servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

/**
 * Valida dados de backup antes da importação
 */
export async function validateBackup(
  request: FastifyRequest<{ Body: { backupData: ServerBackupData } }>,
  reply: FastifyReply,
) {
  try {
    const { backupData } = request.body
    const result: BackupValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    }

    // Validações básicas
    if (!backupData.version) {
      result.errors.push('Versão do backup não especificada')
    }

    if (!backupData.server) {
      result.errors.push('Dados do servidor não encontrados no backup')
    } else {
      // Validar campos obrigatórios do servidor
      if (!backupData.server.name) {
        result.errors.push('Nome do servidor é obrigatório')
      }
      if (!backupData.server.host) {
        result.errors.push('Host do servidor é obrigatório')
      }
      if (!backupData.server.username) {
        result.errors.push('Username do servidor é obrigatório')
      }

      // Verificar se já existe um servidor com o mesmo nome e host
      const existingServer = await prisma.server.findFirst({
        where: {
          name: backupData.server.name,
          host: backupData.server.host,
          userId: request.user.id
        }
      })

      if (existingServer) {
        result.serverExists = true
        result.existingServerId = existingServer.id
        result.warnings.push(`Já existe um servidor com o nome "${backupData.server.name}" e host "${backupData.server.host}"`)
      }
    }

    // Validar comandos
    if (backupData.commands && Array.isArray(backupData.commands)) {
      backupData.commands.forEach((cmd, index) => {
        if (!cmd.name) {
          result.errors.push(`Comando ${index + 1}: Nome é obrigatório`)
        }
        if (!cmd.command) {
          result.errors.push(`Comando ${index + 1}: Comando é obrigatório`)
        }
      })
    }

    result.isValid = result.errors.length === 0

    return reply.send(result)

  } catch (error) {
    console.error('Erro ao validar backup:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

/**
 * Importa um servidor a partir de dados de backup
 */
export async function importServer(
  request: FastifyRequest<{ Body: ImportServerDTO }>,
  reply: FastifyReply,
) {
  try {
    const { backupData, overwriteExisting = false, newServerName } = request.body

    // Validar dados básicos
    if (!backupData || !backupData.server) {
      return reply.status(400).send({ error: 'Dados de backup inválidos' })
    }

    const serverData = backupData.server
    const serverName = newServerName || serverData.name

    // Verificar se servidor já existe
    const existingServer = await prisma.server.findFirst({
      where: {
        name: serverName,
        host: serverData.host,
        userId: request.user.id
      }
    })

    if (existingServer && !overwriteExisting) {
      return reply.status(409).send({ 
        error: 'Servidor já existe. Use a opção de sobrescrever ou escolha um novo nome.' 
      })
    }

    let server: any

    if (existingServer && overwriteExisting) {
      // Atualizar servidor existente
      server = await prisma.server.update({
        where: { id: existingServer.id },
        data: {
          name: serverName,
          host: serverData.host,
          port: serverData.port,
          username: serverData.username,
          password: serverData.password,
          privateKey: serverData.privateKey,
          os: serverData.os,
          deviceType: serverData.deviceType,
          // Remover comandos existentes
          commands: {
            deleteMany: {}
          }
        }
      })

      // Adicionar novos comandos
      if (backupData.commands && backupData.commands.length > 0) {
        await prisma.command.createMany({
          data: backupData.commands.map(cmd => ({
            name: cmd.name,
            command: cmd.command,
            description: cmd.description,
            order: cmd.order,
            serverId: server.id
          }))
        })
      }
    } else {
      // Criar novo servidor
      server = await prisma.server.create({
        data: {
          name: serverName,
          host: serverData.host,
          port: serverData.port,
          username: serverData.username,
          password: serverData.password,
          privateKey: serverData.privateKey,
          os: serverData.os,
          deviceType: serverData.deviceType,
          userId: request.user.id,
          commands: backupData.commands && backupData.commands.length > 0 ? {
            create: backupData.commands.map(cmd => ({
              name: cmd.name,
              command: cmd.command,
              description: cmd.description,
              order: cmd.order
            }))
          } : undefined
        },
        include: {
          commands: true
        }
      })
    }

    // Buscar servidor completo para retorno
    const completeServer = await prisma.server.findUnique({
      where: { id: server.id },
      include: {
        commands: {
          where: { isLatest: true },
          orderBy: { order: 'asc' }
        }
      }
    })

    return reply.status(201).send({
      message: existingServer ? 'Servidor atualizado com sucesso' : 'Servidor importado com sucesso',
      server: completeServer
    })

  } catch (error) {
    console.error('Erro ao importar servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

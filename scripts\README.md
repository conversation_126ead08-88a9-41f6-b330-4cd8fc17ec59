# Scripts de Automação

Esta pasta contém scripts shell para automação e manutenção do projeto.

## Scripts Disponíveis

### `backup-externo.sh`
Script para realizar backup externo dos dados do sistema.

### `configurar-backup-diario.sh`
Script para configurar backup diário automático no sistema.

### `instalar-backup-producao.sh`
Script para instalar e configurar backup no ambiente de produção.

### `monitor.sh`
Script de monitoramento do sistema e serviços.

### `update.sh`
Script para atualização do sistema e dependências.

## Como Usar

Para executar qualquer script, navegue até a pasta scripts e execute:

```bash
cd scripts
chmod +x nome-do-script.sh
./nome-do-script.sh
```

## Observações

- Certifique-se de ter as permissões necessárias antes de executar os scripts
- Alguns scripts podem requerer privilégios de administrador
- Sempre revise o conteúdo dos scripts antes da execução em produção

import { app } from './config/fastify'
import { env } from './config/env'
import { authRoutes } from './routes/auth'
import { userRoutes } from './routes/user.routes'
import { serverRoutes } from './routes/servers'
import { serverUserRoutes } from './routes/serverUsers'
import { commandTemplateRoutes } from './routes/commandTemplates'
import { serverGroupRoutes } from './routes/serverGroups'
import { backupRoutes } from './routes/backup'
import ensureActiveAdmin from './scripts/ensureActiveAdmin'
import { PrismaClient } from '@prisma/client'

// Monitorar uso de memória
if (process.env.NODE_ENV === 'production') {
  try {
    const v8 = require('v8')
    console.log('Configurações de memória V8:', {
      heapSizeLimit: Math.round(v8.getHeapStatistics().heap_size_limit / (1024 * 1024)) + 'MB',
      totalAvailableSize: Math.round(v8.getHeapStatistics().total_available_size / (1024 * 1024)) + 'MB',
    })

    // Configurar intervalo para monitoramento de memória
    setInterval(() => {
      const memoryUsage = process.memoryUsage()
      console.log('Uso de memória:', {
        rss: Math.round(memoryUsage.rss / (1024 * 1024)) + 'MB',
        heapTotal: Math.round(memoryUsage.heapTotal / (1024 * 1024)) + 'MB',
        heapUsed: Math.round(memoryUsage.heapUsed / (1024 * 1024)) + 'MB',
      })
    }, 60000) // A cada 1 minuto
  } catch (e) {
    console.warn('Erro ao configurar monitoramento de memória:', e)
  }
}

// Inicializar o cliente Prisma
const prisma = new PrismaClient()

// Função para verificar a integridade do banco de dados
async function checkDatabaseIntegrity() {
  try {
    console.log('Verificando integridade do banco de dados...')

    // Verificar se existem usuários
    const userCount = await prisma.user.count()
    console.log(`Usuários encontrados: ${userCount}`)

    if (userCount === 0) {
      console.warn('ALERTA: Nenhum usuário encontrado no banco de dados!')
    }

    // Verificar se existem servidores
    const serverCount = await prisma.server.count()
    console.log(`Servidores encontrados: ${serverCount}`)

    // Verificar se existem comandos
    const commandCount = await prisma.command.count()
    console.log(`Comandos encontrados: ${commandCount}`)

    // Verificar se existem servidores sem comandos
    const serversWithoutCommands = await prisma.server.findMany({
      where: {
        commands: {
          none: {}
        }
      },
      select: {
        id: true,
        name: true
      }
    })

    if (serversWithoutCommands.length > 0) {
      console.warn('ALERTA: Encontrados servidores sem comandos:')
      serversWithoutCommands.forEach(server => {
        console.warn(`- ${server.name} (${server.id})`)
      })
    }

    console.log('Verificação de integridade concluída!')
  } catch (error) {
    console.error('Erro ao verificar banco de dados:', error)
  }
}

async function bootstrap() {
  // Verificar a integridade do banco de dados
  await checkDatabaseIntegrity()

  // Garantir que existe pelo menos um administrador ativo
  await ensureActiveAdmin()

  // Registrar rota de health check
  app.get('/health', async (_request, reply) => {
    const memoryUsage = process.memoryUsage()
    return reply.send({
      status: 'ok',
      timestamp: new Date().toISOString(),
      memory: {
        rss: Math.round(memoryUsage.rss / (1024 * 1024)) + 'MB',
        heapTotal: Math.round(memoryUsage.heapTotal / (1024 * 1024)) + 'MB',
        heapUsed: Math.round(memoryUsage.heapUsed / (1024 * 1024)) + 'MB',
        external: Math.round(memoryUsage.external / (1024 * 1024)) + 'MB',
      },
      uptime: process.uptime() + 's',
    })
  })

  await app.register(authRoutes)
  await app.register(userRoutes, { prefix: '/api' })
  await app.register(serverRoutes, { prefix: '/api/servers' })
  await app.register(serverUserRoutes, { prefix: '/api' })
  await app.register(commandTemplateRoutes, { prefix: '/api/command-templates' })
  await app.register(serverGroupRoutes, { prefix: '/api/server-groups' })
  await app.register(backupRoutes, { prefix: '/api/backup' })

  // Configurar hook para monitorar uso de memória em cada requisição
  app.addHook('onResponse', (request, _reply, done) => {
    if (Math.random() < 0.05) { // Amostragem de 5% das requisições para não sobrecarregar os logs
      const memoryUsage = process.memoryUsage()
      console.log(`Uso de memória após ${request.method} ${request.url}:`, {
        rss: Math.round(memoryUsage.rss / (1024 * 1024)) + 'MB',
        heapUsed: Math.round(memoryUsage.heapUsed / (1024 * 1024)) + 'MB',
      })
    }
    done()
  })

  await app.listen({
    port: env.PORT,
    host: env.HOST,
  })
}

bootstrap()
  .then(() => {
    console.log('🚀 HTTP server running!')
  })
  .catch((err) => {
    console.error('❌ Error starting server:', err)
    process.exit(1)
  });
import { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { exportServer, validateBackup, importServer } from '../controllers/backup'
import { verifyJWT } from '../middlewares/auth'

// Schema para validação de dados de backup
const backupDataSchema = z.object({
  version: z.string(),
  exportedAt: z.string(),
  exportedBy: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string()
  }),
  server: z.object({
    name: z.string(),
    host: z.string(),
    port: z.number(),
    username: z.string(),
    password: z.string().optional(),
    privateKey: z.string().optional(),
    os: z.enum(['LINUX', 'WINDOWS']),
    deviceType: z.enum(['NOKIA', 'HUAWEI', 'MIKROTIK', 'DMOS', 'GENERIC'])
  }),
  commands: z.array(z.object({
    name: z.string(),
    command: z.string(),
    description: z.string().optional(),
    order: z.number()
  }))
})

const importServerSchema = z.object({
  backupData: backupDataSchema,
  overwriteExisting: z.boolean().optional().default(false),
  newServerName: z.string().optional()
})

const validateBackupSchema = z.object({
  backupData: backupDataSchema
})

export async function backupRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Exportar servidor como backup
  app.get('/servers/:id/export', exportServer)

  // Validar dados de backup
  app.post('/servers/validate-backup', validateBackup)

  // Importar servidor a partir de backup
  app.post('/servers/import', importServer)
}

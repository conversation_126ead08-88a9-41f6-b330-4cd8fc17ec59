export interface ServerBackupData {
  version: string
  exportedAt: string
  exportedBy: {
    id: string
    name: string
    email: string
  }
  server: {
    name: string
    host: string
    port: number
    username: string
    password?: string
    privateKey?: string
    os: 'LINUX' | 'WINDOWS'
    deviceType: 'NOKIA' | 'HUAWEI' | 'MIKROTIK' | 'DMOS' | 'GENERIC'
  }
  commands: {
    name: string
    command: string
    description?: string
    order: number
  }[]
}

export interface ImportServerDTO {
  backupData: ServerBackupData
  overwriteExisting?: boolean
  newServerName?: string
}

export interface BackupValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  serverExists?: boolean
  existingServerId?: string
}

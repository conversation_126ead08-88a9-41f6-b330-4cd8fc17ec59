import { api } from '../lib/api'
import { 
  ServerBackupData, 
  ImportServerDTO, 
  BackupValidationResult, 
  ImportServerResponse 
} from '../types/backup'

/**
 * Exporta um servidor como backup JSON
 */
export async function exportServer(serverId: string): Promise<ServerBackupData> {
  const response = await api.get(`/api/backup/servers/${serverId}/export`)
  return response.data
}

/**
 * Faz download do backup de um servidor
 */
export async function downloadServerBackup(serverId: string, serverName: string): Promise<void> {
  try {
    const response = await api.get(`/api/backup/servers/${serverId}/export`, {
      responseType: 'blob'
    })
    
    // Criar nome do arquivo
    const fileName = `backup_${serverName.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.json`
    
    // Criar URL do blob e fazer download
    const blob = new Blob([response.data], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Erro ao fazer download do backup:', error)
    throw error
  }
}

/**
 * Valida dados de backup antes da importação
 */
export async function validateBackup(backupData: ServerBackupData): Promise<BackupValidationResult> {
  const response = await api.post('/api/backup/servers/validate-backup', {
    backupData
  })
  return response.data
}

/**
 * Importa um servidor a partir de dados de backup
 */
export async function importServer(data: ImportServerDTO): Promise<ImportServerResponse> {
  const response = await api.post('/api/backup/servers/import', data)
  return response.data
}

/**
 * Lê arquivo JSON de backup
 */
export function readBackupFile(file: File): Promise<ServerBackupData> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (event) => {
      try {
        const content = event.target?.result as string
        const backupData = JSON.parse(content) as ServerBackupData
        resolve(backupData)
      } catch (error) {
        reject(new Error('Arquivo JSON inválido'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Erro ao ler arquivo'))
    }
    
    reader.readAsText(file)
  })
}
